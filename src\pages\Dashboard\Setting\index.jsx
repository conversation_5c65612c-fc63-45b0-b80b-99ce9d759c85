import { useState, useRef, useEffect } from 'react';
import { InputText } from 'primereact/inputtext';
import { Password } from 'primereact/password';
import { But<PERSON> } from 'primereact/button';
import { Toast } from 'primereact/toast';
import { Image } from 'primereact/image';
import { QRCodeSVG } from 'qrcode.react';
import axiosInstance from "../../../config/Axios";
// import jsQR from 'jsqr'; // Removed

// API URL for image storage
const API_URL = import.meta.env.VITE_REACT_APP_API_URL || 'http://192.168.88.79:8000';

// Default user image
const DEFAULT_USER_IMAGE = 'https://storage.inknull.com/uploads/user-image-14-591-1751789627.png';

function SettingsIndex() {
    const toast = useRef(null);
    const fileInputRef = useRef(null);
    // const videoRef = useRef(null); // Removed
    // const canvasRef = useRef(null); // Removed
    const [isEditingProfile, setIsEditingProfile] = useState(false);
    const [isEditingPassword, setIsEditingPassword] = useState(false);
    const [originalName, setOriginalName] = useState('');
    const [originalEmail, setOriginalEmail] = useState('');
    const [loading, setLoading] = useState(false);
    const [imageLoading, setImageLoading] = useState(false);
    const [errors, setErrors] = useState({});
    const [userImage, setUserImage] = useState('');

    // Two-Factor Authentication states
    const [twoFactorStatus, setTwoFactorStatus] = useState({ enabled: false, verified_at: null });
    const [twoFactorLoading, setTwoFactorLoading] = useState(false);
    const [qrCodeData, setQrCodeData] = useState(null);
    const [verificationCode, setVerificationCode] = useState('');
    const [secretKey, setSecretKey] = useState('');
    const [showSetup, setShowSetup] = useState(false);
    const [showDisable, setShowDisable] = useState(false);

    // Form states
    const [name, setName] = useState('');
    const [email, setEmail] = useState('');
    const [currentPassword, setCurrentPassword] = useState('');
    const [newPassword, setNewPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');

    // Professional setup step
    const [setupStep, setSetupStep] = useState(1);
    // Remove qrScanned, isScanning, scanningError, videoRef, canvasRef
    // const [qrScanned, setQrScanned] = useState(false); // Removed
    // const [isScanning, setIsScanning] = useState(false); // Removed
    // const [scanningError, setScanningError] = useState(''); // Removed

    // Fetch user data on component mount
    useEffect(() => {
        fetchUserData();
        fetchTwoFactorStatus();
    }, []);

    // Remove all useEffect and functions related to camera/jsQR/scanQRCode
    // const startQRScanning = async () => { ... }; // Removed
    // const scanQRCode = () => { ... }; // Removed
    // const stopQRScanning = () => { ... }; // Removed

    const fetchUserData = async () => {
        try {
            const userId = localStorage.getItem('user_id');
            const response = await axiosInstance.get(`/users/${userId}`);
            const userData = response.data.data || response.data;

            setName(userData.name || '');
            setEmail(userData.email || '');
            setOriginalName(userData.name || '');
            setOriginalEmail(userData.email || '');
            
            if (userData.image) {
                setUserImage(userData.image);
                localStorage.setItem('user_image', userData.image);
            } else {
                setUserImage('');
                localStorage.removeItem('user_image');
            }
        } catch (error) {
            console.error('Error fetching user data:', error);
            showMessage('error', 'Error', 'Failed to load user data');
        }
    };

    const fetchTwoFactorStatus = async () => {
        try {
            const userId = localStorage.getItem('user_id');
            const token = localStorage.getItem('token');
            
            console.log('🔍 Fetching 2FA status for user:', userId);

            const response = await axiosInstance.get('/two-factor/status', {
                params: { user_id: userId },
                headers: token ? {
                    'Authorization': `Bearer ${token}`
                } : {}
            });
            
            console.log('✅ 2FA Status:', response.data);
            setTwoFactorStatus(response.data);
        } catch (error) {
            console.error('❌ Error fetching two-factor status:', error);
            console.error('❌ Error response:', error.response?.data);
        }
    };

    const showMessage = (severity, summary, detail) => {
        toast.current?.show({ severity, summary, detail, life: 3000 });
    };

    const handleImageUpload = async (event) => {
        const file = event.target.files[0];
        if (!file) return;

        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
            showMessage('error', 'Invalid File Type', 'Please select a valid image file (JPEG, PNG, GIF)');
            return;
        }

        const maxSize = 5 * 1024 * 1024;
        if (file.size > maxSize) {
            showMessage('error', 'File Too Large', 'Image size should be less than 5MB');
            return;
        }

        setImageLoading(true);
        setErrors({});

        try {
            const userId = localStorage.getItem('user_id');
            const formData = new FormData();
            formData.append('image', file);

            const response = await axiosInstance.post(`/users/${userId}/upload-image`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });

            console.log('Image upload response:', response.data);

            const newImageUrl = response.data.data?.image || response.data.image;
            setUserImage(newImageUrl);
            localStorage.setItem('user_image', newImageUrl);
            
            showMessage('success', 'Image Updated', 'Profile image updated successfully');
            await fetchUserData();

        } catch (error) {
            console.error('Error uploading image:', error);
            console.log('Error response:', error.response?.data);

            if (error.response?.data?.details) {
                setErrors(error.response.data.details);
            } else if (error.response?.data?.errors) {
                setErrors(error.response.data.errors);
            }

            showMessage('error', 'Upload Failed', error.response?.data?.message || 'Failed to upload image');
        } finally {
            setImageLoading(false);
        }
    };

    const handleUpdateProfile = async () => {
        setLoading(true);
        setErrors({});

        try {
            const userId = localStorage.getItem('user_id');

            console.log('Sending profile update data:', {
                name: name,
                email: email
            });

            const response = await axiosInstance.put(`/users/${userId}`, {
                name: name,
                email: email
            });

            console.log('Profile update response:', response.data);

            showMessage('success', 'Profile Updated', 'Changes saved successfully');
            setIsEditingProfile(false);
            setOriginalName(name);
            setOriginalEmail(email);
        } catch (error) {
            console.error('Error updating profile:', error);
            console.log('Error response:', error.response?.data);

            if (error.response?.data?.details) {
                setErrors(error.response.data.details);
            } else if (error.response?.data?.errors) {
                setErrors(error.response.data.errors);
            }

            showMessage('error', 'Update Failed', error.response?.data?.message || 'Failed to update profile');
        } finally {
            setLoading(false);
        }
    };

    const handleChangePassword = async () => {
        if (newPassword !== confirmPassword) {
            showMessage('error', 'Error', 'Passwords do not match');
            setErrors({ confirmPassword: 'Passwords do not match' });
            return;
        }

        setLoading(true);
        setErrors({});

        try {
            const userId = localStorage.getItem('user_id');

            const passwordData = {
                id: userId,
                current_password: currentPassword,
                new_password: newPassword,
                new_password_confirmation: confirmPassword
            };

            const response = await axiosInstance.post(`/users/change-password`, passwordData);

            console.log('Password change response:', response.data);

            showMessage('success', 'Password Changed', 'Password updated successfully');
            setIsEditingPassword(false);
            setCurrentPassword('');
            setNewPassword('');
            setConfirmPassword('');
        } catch (error) {
            console.error('Error changing password:', error);
            console.log('Error response:', error.response?.data);

            if (error.response?.data?.details) {
                setErrors(error.response.data.details);
            } else if (error.response?.data?.errors) {
                setErrors(error.response.data.errors);
            }

            showMessage('error', 'Update Failed', error.response?.data?.message || 'Failed to update password');
        } finally {
            setLoading(false);
        }
    };

    const handleTwoFactorSetup = async () => {
        setTwoFactorLoading(true);
        try {
            // Get user authentication data
            const userId = localStorage.getItem('user_id');
            const token = localStorage.getItem('token');
            
            console.log('🔍 Starting 2FA setup for user:', userId);

            const response = await axiosInstance.post('/two-factor/setup', {
                user_id: userId
            }, {
                headers: token ? {
                    'Authorization': `Bearer ${token}`
                } : {}
            });
            
            console.log('✅ QR Code Setup Response:', response.data);
            console.log('📱 QR URL:', response.data.qr_url);
            console.log('🔑 Secret Key:', response.data.secret);
            
            setQrCodeData(response.data);
            setSecretKey(response.data.secret);
            setShowSetup(true);
            setSetupStep(1); // Start with step 1 (QR code display)
        } catch (error) {
            console.error('❌ Error setting up two-factor:', error);
            console.error('❌ Error response:', error.response?.data);
            console.error('❌ Error status:', error.response?.status);
            
            const errorMessage = error.response?.data?.message || 'Failed to setup two-factor authentication';
            console.log('🔍 Setup failed with message:', errorMessage);
            
            showMessage('error', 'Setup Failed', errorMessage);
        } finally {
            setTwoFactorLoading(false);
        }
    };

    const handleTwoFactorVerify = async () => {
        if (!verificationCode || verificationCode.length !== 6) {
            showMessage('error', 'Invalid Code', 'Please enter a valid 6-digit code');
            return;
        }

        setTwoFactorLoading(true);
        try {
            // Get user authentication data
            const userId = localStorage.getItem('user_id');
            const token = localStorage.getItem('token');
            
            console.log('🔍 Verifying 2FA setup:', {
                code: verificationCode,
                userId: userId,
                hasToken: !!token
            });

            await axiosInstance.post('/two-factor/verify', {
                code: verificationCode,
                user_id: userId
            }, {
                headers: token ? {
                    'Authorization': `Bearer ${token}`
                } : {}
            });
            
            console.log('✅ 2FA setup verification successful!');
            showMessage('success', 'Success', 'Two-factor authentication enabled successfully');
            setShowSetup(false);
            setVerificationCode('');
            setQrCodeData(null);
            setSecretKey('');
            // setQrScanned(false); // Removed
            // stopQRScanning(); // Removed
            await fetchTwoFactorStatus();
            setSetupStep(3); // Automatically progress to step 3
        } catch (error) {
            console.error('❌ Error verifying two-factor:', error);
            console.error('❌ Error response:', error.response?.data);
            console.error('❌ Error status:', error.response?.status);
            
            const errorMessage = error.response?.data?.message || 'Invalid verification code';
            console.log('🔍 Setup verification failed with message:', errorMessage);
            
            showMessage('error', 'Verification Failed', errorMessage);
        } finally {
            setTwoFactorLoading(false);
        }
    };

    const handleTwoFactorDisable = async () => {
        if (!verificationCode || verificationCode.length !== 6) {
            showMessage('error', 'Invalid Code', 'Please enter a valid 6-digit code');
            return;
        }

        setTwoFactorLoading(true);
        try {
            // Get user authentication data
            const userId = localStorage.getItem('user_id');
            const token = localStorage.getItem('token');
            
            console.log('🔍 Disabling 2FA:', {
                code: verificationCode,
                userId: userId,
                hasToken: !!token
            });

            await axiosInstance.post('/two-factor/disable', {
                code: verificationCode,
                user_id: userId
            }, {
                headers: token ? {
                    'Authorization': `Bearer ${token}`
                } : {}
            });
            
            console.log('✅ 2FA disabled successfully!');
            showMessage('success', 'Success', 'Two-factor authentication disabled successfully');
            setShowDisable(false);
            setVerificationCode('');
            await fetchTwoFactorStatus();
        } catch (error) {
            console.error('❌ Error disabling two-factor:', error);
            console.error('❌ Error response:', error.response?.data);
            console.error('❌ Error status:', error.response?.status);
            
            const errorMessage = error.response?.data?.message || 'Invalid verification code';
            console.log('🔍 Disable verification failed with message:', errorMessage);
            
            showMessage('error', 'Disable Failed', errorMessage);
        } finally {
            setTwoFactorLoading(false);
        }
    };

    const enterEditProfileMode = () => {
        setOriginalName(name);
        setOriginalEmail(email);
        setIsEditingProfile(true);
    };

    const cancelEditProfile = () => {
        setName(originalName);
        setEmail(originalEmail);
        setIsEditingProfile(false);
    };

    const enterEditImageMode = () => {
        setTimeout(() => {
            fileInputRef.current?.click();
        }, 100);
    };

    const renderTwoFactorSection = () => (
        <div className="bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 rounded-3xl p-8 border border-white/20 shadow-2xl backdrop-blur-xl relative">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-30">
                <div className="w-full h-full" style={{
                    backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
                }}></div>
            </div>
            
            {/* Animated Background Elements */}
            <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
                <div className="absolute -top-20 -right-20 w-40 h-40 bg-gradient-to-br from-blue-500/10 to-purple-600/10 rounded-full blur-2xl animate-pulse"></div>
                <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-gradient-to-tr from-purple-500/10 to-pink-600/10 rounded-full blur-2xl animate-pulse delay-1000"></div>
            </div>

            <div className="relative z-10">
                <div className="flex items-center gap-4 mb-8">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-2xl relative overflow-hidden">
                        <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>
                        <i className="pi pi-shield text-white text-2xl relative z-10"></i>
                        <div className="absolute inset-0 bg-gradient-to-br from-transparent to-black/20"></div>
                    </div>
                    <div>
                        <h3 className="text-2xl font-bold text-white mb-2">Two-Factor Authentication</h3>
                        <p className="text-gray-300">Secure your account with an additional layer of protection</p>
                    </div>
                </div>

                {!showSetup && !showDisable && (
                    <div className="grid lg:grid-cols-2 gap-8">
                        {/* Left Column - Status & Actions */}
                        <div className="space-y-6">
                            {/* Status Card */}
                            <div className={`rounded-2xl p-6 border ${
                                twoFactorStatus.enabled 
                                    ? 'bg-gradient-to-r from-emerald-500/20 to-blue-500/20 border-emerald-400/30' 
                                    : 'bg-gradient-to-r from-amber-500/20 to-orange-500/20 border-amber-400/30'
                            }`}>
                                <div className="flex items-center gap-4">
                                    <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                                        twoFactorStatus.enabled 
                                            ? 'bg-emerald-500/20' 
                                            : 'bg-amber-500/20'
                                    }`}>
                                        <i className={`text-xl ${
                                            twoFactorStatus.enabled 
                                                ? 'pi pi-check-circle text-emerald-400' 
                                                : 'pi pi-exclamation-triangle text-amber-400'
                                        }`}></i>
                                    </div>
                                    <div>
                                        <h4 className={`text-lg font-semibold mb-1 ${
                                            twoFactorStatus.enabled ? 'text-white' : 'text-white'
                                        }`}>
                                            {twoFactorStatus.enabled ? '2FA is Enabled' : '2FA is Disabled'}
                                        </h4>
                                        <p className={`text-sm ${
                                            twoFactorStatus.enabled ? 'text-emerald-300' : 'text-amber-300'
                                        }`}>
                                            {twoFactorStatus.enabled 
                                                ? 'Your account is protected with two-factor authentication'
                                                : 'Enable 2FA to add an extra layer of security to your account'
                                            }
                                        </p>
                                    </div>
                                </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="space-y-4">
                                {!twoFactorStatus.enabled ? (
                                    <Button
                                        label="Enable Two-Factor Authentication"
                                        icon="pi pi-shield"
                                        className="w-full py-4 text-lg font-semibold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 hover:from-blue-700 hover:via-purple-700 hover:to-pink-700 hover:scale-105 active:scale-95 border-0 shadow-2xl rounded-2xl transition-all duration-300 text-white"
                                        onClick={handleTwoFactorSetup}
                                        loading={twoFactorLoading}
                                    />
                                ) : (
                                    <Button
                                        label="Disable Two-Factor Authentication"
                                        icon="pi pi-lock-open"
                                        severity="danger"
                                        className="w-full py-4 text-lg font-semibold bg-gradient-to-r from-red-600 via-orange-600 to-pink-600 hover:from-red-700 hover:via-orange-700 hover:to-pink-700 hover:scale-105 active:scale-95 border-0 shadow-2xl rounded-2xl transition-all duration-300 text-white"
                                        onClick={() => setShowDisable(true)}
                                        loading={twoFactorLoading}
                                    />
                                )}
                            </div>

                            {/* Security Features */}
                            <div className="bg-gradient-to-r from-emerald-500/20 to-blue-500/20 rounded-2xl p-6 border border-emerald-400/30">
                                <div className="flex items-center gap-4 mb-4">
                                    <i className="pi pi-shield text-emerald-400 text-2xl"></i>
                                    <h4 className="text-lg font-bold text-white">Security Features</h4>
                                </div>
                                <div className="grid grid-cols-2 gap-3 text-sm">
                                    <div className="flex items-center gap-2 text-emerald-300">
                                        <i className="pi pi-check-circle"></i>
                                        <span>30-second codes</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-emerald-300">
                                        <i className="pi pi-check-circle"></i>
                                        <span>3 attempt limit</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-emerald-300">
                                        <i className="pi pi-check-circle"></i>
                                        <span>Auto-lock protection</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-emerald-300">
                                        <i className="pi pi-check-circle"></i>
                                        <span>Secure encryption</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Right Column - Information */}
                        <div className="space-y-6">
                            {/* How it Works */}
                            <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20">
                                <div className="flex items-start gap-4 mb-6">
                                    <div className="w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i className="pi pi-info-circle text-blue-400 text-xl"></i>
                                    </div>
                                    <div>
                                        <h4 className="text-xl font-bold text-white mb-2">How 2FA Works</h4>
                                        <p className="text-gray-300 text-sm leading-relaxed">
                                            Two-factor authentication adds an extra layer of security by requiring a second form of verification in addition to your password.
                                        </p>
                                    </div>
                                </div>
                                
                                <div className="space-y-3">
                                    <div className="flex items-center gap-3 p-3 bg-white/5 rounded-xl">
                                        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-xs">1</div>
                                        <span className="text-white text-sm">Scan QR code with authenticator app</span>
                                    </div>
                                    <div className="flex items-center gap-3 p-3 bg-white/5 rounded-xl">
                                        <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold text-xs">2</div>
                                        <span className="text-white text-sm">Enter 6-digit code to verify setup</span>
                                    </div>
                                    <div className="flex items-center gap-3 p-3 bg-white/5 rounded-xl">
                                        <div className="w-6 h-6 bg-pink-500 rounded-full flex items-center justify-center text-white font-bold text-xs">3</div>
                                        <span className="text-white text-sm">Use app codes for future logins</span>
                                    </div>
                                </div>
                            </div>

                            {/* Supported Apps */}
                            <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20">
                                <div className="flex items-start gap-4 mb-4">
                                    <div className="w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i className="pi pi-mobile text-purple-400 text-xl"></i>
                                    </div>
                                    <div>
                                        <h4 className="text-lg font-bold text-white mb-2">Supported Apps</h4>
                                        <p className="text-gray-300 text-sm">Use any TOTP-compatible authenticator app</p>
                                    </div>
                                </div>
                                
                                <div className="grid grid-cols-2 gap-3 text-sm">
                                    <div className="flex items-center gap-2 text-purple-300">
                                        <i className="pi pi-check-circle"></i>
                                        <span>Google Authenticator</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-purple-300">
                                        <i className="pi pi-check-circle"></i>
                                        <span>Authy</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-purple-300">
                                        <i className="pi pi-check-circle"></i>
                                        <span>Microsoft Authenticator</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-purple-300">
                                        <i className="pi pi-check-circle"></i>
                                        <span>1Password</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Professional Step-by-Step Setup */}
                {showSetup && (
                    <div className="space-y-8">
                        {/* Step Progress Indicator */}
                        <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20">
                            <div className="flex items-center justify-between mb-6">
                                <h3 className="text-xl font-bold text-white">Setup Progress</h3>
                                <div className="text-sm text-gray-300">
                                    Step {setupStep} of 3
                                </div>
                            </div>
                            
                            {/* Progress Bar */}
                            <div className="w-full bg-white/10 rounded-full h-3 mb-6">
                                <div 
                                    className="bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 h-3 rounded-full transition-all duration-500 ease-out"
                                    style={{ width: `${(setupStep / 3) * 100}%` }}
                                ></div>
                            </div>
                            
                            {/* Step Indicators */}
                            <div className="flex items-center justify-between">
                                <div className={`flex items-center gap-3 ${setupStep >= 1 ? 'text-blue-400' : 'text-gray-500'}`}>
                                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                                        setupStep >= 1 
                                            ? 'bg-blue-500 text-white' 
                                            : 'bg-white/10 text-gray-400'
                                    }`}>
                                        {setupStep > 1 ? <i className="pi pi-check text-xs"></i> : '1'}
                                    </div>
                                    <span className="text-sm font-medium">QR Code Scan</span>
                                </div>
                                
                                <div className={`flex-1 h-0.5 mx-4 ${
                                    setupStep >= 2 ? 'bg-blue-500' : 'bg-white/10'
                                }`}></div>
                                
                                <div className={`flex items-center gap-3 ${setupStep >= 2 ? 'text-purple-400' : 'text-gray-500'}`}>
                                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                                        setupStep >= 2 
                                            ? 'bg-purple-500 text-white' 
                                            : 'bg-white/10 text-gray-400'
                                    }`}>
                                        {setupStep > 2 ? <i className="pi pi-check text-xs"></i> : '2'}
                                    </div>
                                    <span className="text-sm font-medium">Verification</span>
                                </div>
                                
                                <div className={`flex-1 h-0.5 mx-4 ${
                                    setupStep >= 3 ? 'bg-purple-500' : 'bg-white/10'
                                }`}></div>
                                
                                <div className={`flex items-center gap-3 ${setupStep >= 3 ? 'text-pink-400' : 'text-gray-500'}`}>
                                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                                        setupStep >= 3 
                                            ? 'bg-pink-500 text-white' 
                                            : 'bg-white/10 text-gray-400'
                                    }`}>
                                        {setupStep > 3 ? <i className="pi pi-check text-xs"></i> : '3'}
                                    </div>
                                    <span className="text-sm font-medium">Complete</span>
                                </div>
                            </div>
                        </div>

                        {/* Step 1: QR Code Scan */}
                        {setupStep === 1 && (
                            <div className="space-y-6">
                        <div className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-400/30 rounded-2xl p-6">
                            <div className="flex items-start gap-4">
                                <div className="w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                            <i className="pi pi-qrcode text-blue-400 text-xl"></i>
                                </div>
                                <div className="flex-1">
                                            <h3 className="text-xl font-semibold text-white mb-3">Step 1: Scan QR Code</h3>
                                    <div className="space-y-3">
                                        <div className="flex items-center gap-3 p-3 bg-white/10 rounded-xl">
                                            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-sm">1</div>
                                            <span className="text-white">Open your authenticator app (Google Authenticator, Authy, etc.)</span>
                                        </div>
                                        <div className="flex items-center gap-3 p-3 bg-white/10 rounded-xl">
                                            <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold text-sm">2</div>
                                            <span className="text-white">Scan the QR code below with your app</span>
                                        </div>
                                        <div className="flex items-center gap-3 p-3 bg-white/10 rounded-xl">
                                            <div className="w-8 h-8 bg-pink-500 rounded-full flex items-center justify-center text-white font-bold text-sm">3</div>
                                            <span className="text-white">
                                                {/* Removed QR code scanning indicators */}
                                                Point your camera at the QR code below
                                            </span>
                                        </div>
                                        
                                        {/* Removed Camera scanning indicator */}
                                        
                                        {/* Removed Scanning error */}
                                        
                                        {/* Hidden video and canvas elements for QR scanning */}
                                        <div className="hidden">
                                            <video 
                                                // ref={videoRef} 
                                                autoPlay 
                                                playsInline 
                                                muted 
                                                style={{ width: '1px', height: '1px' }}
                                            />
                                            <canvas 
                                                // ref={canvasRef} 
                                                style={{ display: 'none' }}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* QR Code Section */}
                        {qrCodeData && (
                            <div className="flex justify-center">
                                <div className="bg-white/10 backdrop-blur-xl p-8 rounded-2xl border-2 border-white/20 shadow-2xl relative overflow-hidden">
                                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10"></div>
                                    <div className="relative">
                                        <div className="text-center mb-4">
                                            <h4 className="font-semibold text-white mb-1">QR Code</h4>
                                            <p className="text-xs text-gray-300">Scan with your authenticator app</p>
                                        </div>
                                        <div className="bg-white p-6 rounded-xl border shadow-lg">
                                            <QRCodeSVG 
                                                value={`otpauth://totp/InkNull:${email}?secret=${secretKey}&issuer=InkNull`}
                                                size={280}
                                                level="M"
                                                includeMargin={true}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Manual Entry Section */}
                        <div className="bg-gradient-to-r from-gray-500/20 to-blue-500/20 border border-gray-400/30 rounded-2xl p-6">
                            <div className="flex items-start gap-4">
                                <div className="w-12 h-12 bg-gray-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                    <i className="pi pi-key text-gray-400 text-xl"></i>
                                </div>
                                <div className="flex-1">
                                    <h3 className="text-lg font-semibold text-white mb-2">Manual Entry (Alternative)</h3>
                                    <p className="text-gray-300 mb-4">
                                        If QR code scanning doesn&apos;t work, you can manually enter the setup key in your authenticator app
                                    </p>
                                    
                                    <div className="bg-white/10 backdrop-blur-xl rounded-lg p-4 border border-white/20">
                                        <div className="flex items-center gap-3">
                                            <div className="flex-1">
                                                <label className="text-xs font-medium text-gray-300 mb-2 block">Setup Key:</label>
                                                <div className="font-mono text-sm bg-white/10 p-3 rounded-lg border border-white/20 break-all text-white">
                                                    {secretKey}
                                                </div>
                                            </div>
                                            <Button 
                                                icon="pi pi-copy" 
                                                className="p-button-sm p-button-outlined bg-white/10 border-white/20 text-white hover:bg-white/20"
                                                onClick={() => {
                                                    navigator.clipboard.writeText(secretKey);
                                                    showMessage('success', 'Copied!', 'Setup key copied to clipboard');
                                                }}
                                                tooltip="Copy to clipboard"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                                {/* Step 1 Action Buttons */}
                                <div className="flex gap-4 justify-end pt-4 border-t border-white/20">
                                    <Button 
                                        label="Cancel Setup" 
                                        severity="secondary" 
                                        className="px-6 py-3 bg-gradient-to-r from-gray-500/20 to-gray-600/20 border border-gray-400/30 text-white hover:from-gray-600/30 hover:to-gray-700/30 hover:scale-105 active:scale-95 transition-all duration-300 rounded-xl"
                                        onClick={() => {
                                            setShowSetup(false);
                                            setQrCodeData(null);
                                            setSecretKey('');
                                            setVerificationCode('');
                                            setSetupStep(1);
                                            // setQrScanned(false); // Removed
                                            // stopQRScanning(); // Removed
                                        }}
                                    />
                                    <Button 
                                        label="Next Step" 
                                        icon="pi pi-arrow-right"
                                        className="px-6 py-3 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 hover:from-blue-700 hover:via-purple-700 hover:to-pink-700 border-0 hover:scale-105 active:scale-95 transition-all duration-300 rounded-xl text-white font-semibold shadow-xl"
                                        onClick={() => setSetupStep(2)}
                                    />
                                </div>
                            </div>
                        )}

                        {/* Step 2: Verification */}
                        {setupStep === 2 && (
                            <div className="space-y-6">
                                <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-400/30 rounded-2xl p-6">
                            <div className="flex items-start gap-4">
                                        <div className="w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                            <i className="pi pi-check-circle text-purple-400 text-xl"></i>
                                </div>
                                <div className="flex-1">
                                            <h3 className="text-xl font-semibold text-white mb-3">Step 2: Verify Setup</h3>
                                            <p className="text-purple-300 mb-4">
                                                Enter the 6-digit code from your authenticator app to verify the setup is working correctly
                                    </p>
                                    
                                            <div className="bg-white/10 backdrop-blur-xl rounded-lg p-6 border border-white/20">
                                                <div className="space-y-4">
                                            <div>
                                                        <label className="text-sm font-medium text-white block mb-3">Verification Code:</label>
                                                <InputText
                                                    value={verificationCode}
                                                    onChange={(e) => setVerificationCode(e.target.value)}
                                                    placeholder="000000"
                                                    maxLength={6}
                                                            className="w-full text-center text-3xl font-mono tracking-widest border-2 border-purple-400/50 rounded-xl p-4 focus:border-purple-400 focus:ring-4 focus:ring-purple-400/20 bg-white/10 backdrop-blur-sm text-white placeholder-gray-400"
                                                    keyfilter="int"
                                                            autoFocus
                                                />
                                            </div>
                                                    <div className="flex items-center gap-2 text-sm text-purple-300">
                                                <i className="pi pi-clock"></i>
                                                <span>Code refreshes every 30 seconds</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                                {/* Step 2 Action Buttons */}
                        <div className="flex gap-4 justify-end pt-4 border-t border-white/20">
                            <Button 
                                        label="Previous Step" 
                                        icon="pi pi-arrow-left"
                                severity="secondary" 
                                className="px-6 py-3 bg-gradient-to-r from-gray-500/20 to-gray-600/20 border border-gray-400/30 text-white hover:from-gray-600/30 hover:to-gray-700/30 hover:scale-105 active:scale-95 transition-all duration-300 rounded-xl"
                                        onClick={() => setSetupStep(1)}
                                    />
                                    <Button 
                                        label="Verify & Continue" 
                                        icon="pi pi-check"
                                        className="px-6 py-3 bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 hover:from-purple-700 hover:via-pink-700 hover:to-red-700 border-0 hover:scale-105 active:scale-95 transition-all duration-300 rounded-xl text-white font-semibold shadow-xl"
                                        onClick={handleTwoFactorVerify}
                                        loading={twoFactorLoading}
                                        disabled={!verificationCode || verificationCode.length !== 6}
                                    />
                                </div>
                            </div>
                        )}

                        {/* Step 3: Completion */}
                        {setupStep === 3 && (
                            <div className="space-y-6">
                                <div className="bg-gradient-to-r from-emerald-500/20 to-green-500/20 border border-emerald-400/30 rounded-2xl p-6">
                                    <div className="flex items-start gap-4">
                                        <div className="w-12 h-12 bg-emerald-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                            <i className="pi pi-check-circle text-emerald-400 text-xl"></i>
                                        </div>
                                        <div className="flex-1">
                                            <h3 className="text-xl font-semibold text-white mb-3">Step 3: Setup Complete!</h3>
                                            <p className="text-emerald-300 mb-4">
                                                Congratulations! Your two-factor authentication has been successfully enabled. Your account is now protected with an additional layer of security.
                                            </p>
                                            
                                            <div className="bg-white/10 backdrop-blur-xl rounded-lg p-6 border border-white/20">
                                                <div className="space-y-4">
                                                    <div className="flex items-center gap-3 text-emerald-300">
                                                        <i className="pi pi-check-circle"></i>
                                                        <span>QR code scanned successfully</span>
                                                    </div>
                                                    <div className="flex items-center gap-3 text-emerald-300">
                                                        <i className="pi pi-check-circle"></i>
                                                        <span>Verification code confirmed</span>
                                                    </div>
                                                    <div className="flex items-center gap-3 text-emerald-300">
                                                        <i className="pi pi-check-circle"></i>
                                                        <span>2FA enabled for your account</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Step 3 Action Buttons */}
                                <div className="flex gap-4 justify-end pt-4 border-t border-white/20">
                                    <Button 
                                        label="Finish Setup" 
                                        icon="pi pi-check"
                                        className="px-6 py-3 bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 border-0 hover:scale-105 active:scale-95 transition-all duration-300 rounded-xl text-white font-semibold shadow-xl"
                                onClick={() => {
                                    setShowSetup(false);
                                    setQrCodeData(null);
                                    setSecretKey('');
                                    setVerificationCode('');
                                            setSetupStep(1);
                                            // setQrScanned(false); // Removed
                                            // stopQRScanning(); // Removed
                                        }}
                            />
                        </div>
                            </div>
                        )}
                    </div>
                )}

                {/* Disable Section */}
                {showDisable && (
                    <div className="space-y-6">
                        <div className="bg-gradient-to-r from-red-500/20 to-orange-500/20 border border-red-400/30 rounded-2xl p-6">
                            <div className="flex items-start gap-4">
                                <div className="w-12 h-12 bg-red-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                    <i className="pi pi-exclamation-triangle text-red-400 text-xl"></i>
                                </div>
                                <div className="flex-1">
                                    <h3 className="text-lg font-semibold text-white mb-2">Security Warning</h3>
                                    <p className="text-red-300 mb-4">
                                        Disabling two-factor authentication will significantly reduce the security of your account. 
                                        This action should only be performed if you&apos;re experiencing issues with your authenticator app.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div className="bg-gradient-to-r from-gray-500/20 to-blue-500/20 border border-gray-400/30 rounded-2xl p-6">
                            <div className="flex items-start gap-4">
                                <div className="w-12 h-12 bg-gray-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                    <i className="pi pi-key text-gray-400 text-xl"></i>
                                </div>
                                <div className="flex-1">
                                    <h3 className="text-lg font-semibold text-white mb-2">Verification Required</h3>
                                    <p className="text-gray-300 mb-4">
                                        To confirm this action, please enter the current 6-digit code from your authenticator app
                                    </p>
                                    
                                    <div className="bg-white/10 backdrop-blur-xl rounded-lg p-4 border border-white/20">
                                        <div className="space-y-3">
                                            <div>
                                                <label className="text-sm font-medium text-white block mb-2">Current Code:</label>
                                                <InputText
                                                    value={verificationCode}
                                                    onChange={(e) => setVerificationCode(e.target.value)}
                                                    placeholder="000000"
                                                    maxLength={6}
                                                    className="w-full text-center text-2xl font-mono tracking-widest border-2 border-red-400/50 rounded-lg p-4 focus:border-red-400 focus:ring-2 focus:ring-red-400/20 bg-white/10 backdrop-blur-sm text-white placeholder-gray-400"
                                                    keyfilter="int"
                                                />
                                            </div>
                                            <div className="flex items-center gap-2 text-sm text-gray-300">
                                                <i className="pi pi-clock"></i>
                                                <span>Code refreshes every 30 seconds</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="flex gap-4 justify-end pt-4 border-t border-white/20">
                            <Button 
                                label="Keep 2FA Enabled" 
                                severity="secondary" 
                                className="px-6 py-3 bg-gradient-to-r from-gray-500/20 to-gray-600/20 border border-gray-400/30 text-white hover:from-gray-600/30 hover:to-gray-700/30 hover:scale-105 active:scale-95 transition-all duration-300 rounded-xl"
                                onClick={() => {
                                    setShowDisable(false);
                                    setVerificationCode('');
                                }}
                            />
                            <Button 
                                label="Disable 2FA" 
                                severity="danger"
                                className="px-6 py-3 bg-gradient-to-r from-red-600 via-orange-600 to-pink-600 hover:from-red-700 hover:via-orange-700 hover:to-pink-700 border-0 hover:scale-105 active:scale-95 transition-all duration-300 rounded-xl text-white font-semibold shadow-xl"
                                onClick={handleTwoFactorDisable}
                                loading={twoFactorLoading}
                                disabled={!verificationCode || verificationCode.length !== 6}
                            />
                        </div>
                    </div>
                )}
            </div>
        </div>
    );

    return (
        <section className='w-full flex flex-col p-5 h-[95vh] overflow-y-auto bg-white' >
            <Toast ref={toast} />

            <div className="w-full">
                <div className="mb-8 text-left">
                    <h2 className="text-3xl font-bold text-gray-800 flex items-center justify-start gap-3">
                        <i className="pi pi-user bg-blue-100 rounded-full text-blue-600 text-3xl"></i>
                        Account Settings
                    </h2>
                    <p className="text-gray-500 mt-2">Manage your personal information and security preferences</p>
                </div>

                <div className="bg-white rounded-xl shadow-md p-8 mb-8 transition-all hover:shadow-lg border border-gray-100">
                    <div className="flex flex-col md:flex-row gap-8 mb-4">
                        <div className="flex flex-col items-center">
                            <div className="relative group">
                                <div className="w-[130px] h-[130px] rounded-full border-4 border-blue-50 shadow-lg overflow-hidden bg-gradient-to-br from-blue-50 to-purple-50 relative">
                                    <Image 
                                        src={userImage ? (userImage.startsWith('http') ? userImage : `${API_URL}/storage/${userImage}`) : DEFAULT_USER_IMAGE} 
                                        alt="profile"
                                        imageClassName="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110" 
                                        width="130" 
                                        height="130"
                                        preview={false}
                                        onError={(e) => {
                                            e.target.src = DEFAULT_USER_IMAGE;
                                        }}
                                    />
                                    
                                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                    
                                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                                        <div className="bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-lg">
                                            <i className="pi pi-camera text-blue-600 text-lg"></i>
                                        </div>
                                    </div>
                                </div>
                                
                                <Button 
                                    icon="pi pi-camera" 
                                    className="p-button-rounded p-button-primary absolute -bottom-2 -right-2 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 border-2 border-white"
                                    tooltip="Change Photo" 
                                    tooltipOptions={{position: 'bottom'}}
                                    onClick={enterEditImageMode}
                                />
                                
                                {imageLoading && (
                                    <div className="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center">
                                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
                                    </div>
                                )}
                                
                                <input
                                    ref={fileInputRef}
                                    type="file"
                                    accept="image/*"
                                    onChange={handleImageUpload}
                                    className="hidden"
                                />
                            </div>
                        </div>
                        <div className="flex-1">
                            {!isEditingProfile ? (
                                <>
                                    <div className="flex justify-between items-center mb-6">
                                        <h3 className="text-xl font-semibold text-gray-800">Profile Information</h3>
                                        <Button icon="pi pi-pencil"
                                            className="p-button-rounded p-button-outlined p-button-primary"
                                            onClick={enterEditProfileMode} />
                                    </div>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div className="field bg-white p-4 rounded-lg">
                                            <label className="text-sm font-medium text-gray-500 block mb-1">Name</label>
                                            <p className="text-gray-800 font-medium">{name || 'No name provided'}</p>
                                        </div>
                                        <div className="field bg-white p-4 rounded-lg">
                                            <label className="text-sm font-medium text-gray-500 block mb-1">Email</label>
                                            <p className="text-gray-800 font-medium">{email || 'No email provided'}</p>
                                        </div>
                                    </div>
                                </>
                            ) : (
                                <>
                                    <div className="flex justify-between items-center mb-6">
                                        <h3 className="text-xl font-semibold text-gray-800">Edit Profile</h3>
                                        <Button icon="pi pi-times"
                                            className="p-button-rounded p-button-outlined p-button-danger"
                                            onClick={cancelEditProfile} />
                                    </div>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div className="field">
                                            <label className="text-sm font-medium text-gray-700 block mb-2">Name</label>
                                            <InputText
                                                value={name}
                                                onChange={(e) => setName(e.target.value)}
                                                className={`w-full p-inputtext-sm p-3 rounded-lg border ${errors.name ? 'border-red-500' : 'border-gray-300'}`}
                                            />
                                            {errors.name && <small className="p-error block mt-1">{errors.name}</small>}
                                        </div>
                                        <div className="field">
                                            <label className="text-sm font-medium text-gray-700 block mb-2">Email</label>
                                            <InputText
                                                type="email"
                                                value={email}
                                                onChange={(e) => setEmail(e.target.value)}
                                                className={`w-full p-inputtext-sm p-3 rounded-lg border ${errors.email ? 'border-red-500' : 'border-gray-300'}`}
                                            />
                                            {errors.email && <small className="p-error block mt-1">{errors.email}</small>}
                                        </div>
                                    </div>
                                    <div className="flex gap-3 justify-end mt-6">
                                        <Button label="Cancel" severity="secondary" className="p-button-sm"
                                            onClick={cancelEditProfile} />
                                        <Button label="Save Changes" className="p-button-sm main-btn"
                                            onClick={handleUpdateProfile}
                                            loading={loading} />
                                    </div>
                                </>
                            )}
                        </div>
                    </div>
                </div>

                <div className="w-full border-t border-gray-200 my-10 mx-0"></div>

                <div className="text-left mb-8">
                    <h2 className="text-3xl font-bold text-gray-800 flex items-center justify-start gap-3">
                        <i className="pi pi-lock bg-purple-100 rounded-full text-purple-600 text-3xl"></i>
                        Security Settings
                    </h2>
                    <p className="text-gray-500 mt-2">Protect your account with strong security measures</p>
                </div>

                {renderTwoFactorSection()}

                <div className="mt-8">
                    <div className="bg-white rounded-xl shadow-md p-8 transition-all hover:shadow-lg border border-gray-100">
                    {!isEditingPassword ? (
                        <div className="flex justify-between items-center">
                            <div className="text-left">
                                <h3 className="text-xl font-semibold text-gray-800 mb-2">Password</h3>
                                <p className="text-gray-500">Secure your account with a strong password</p>
                            </div>
                            <Button label="Change Password" icon="pi pi-lock"
                                className="p-button-outlined p-button-primary"
                                onClick={() => setIsEditingPassword(true)} />
                        </div>
                    ) : (
                        <>
                            <div className="flex justify-between items-center mb-6">
                                <h3 className="text-xl font-semibold text-gray-800">Change Password</h3>
                                <Button icon="pi pi-times"
                                    className="p-button-rounded p-button-outlined p-button-danger"
                                    onClick={() => setIsEditingPassword(false)} />
                            </div>
                            <div className="grid grid-cols-1 gap-6">
                                    <div className="field bg-white p-4 rounded-lg">
                                    <label className="text-sm font-medium text-gray-700 block mb-2">Current Password</label>
                                    <Password
                                        value={currentPassword}
                                        onChange={(e) => setCurrentPassword(e.target.value)}
                                        toggleMask
                                        className={`w-full ${errors.current_password ? 'p-invalid' : ''}`}
                                        inputClassName="w-full p-inputtext-sm p-3 rounded-lg border border-gray-300"
                                        feedback={false}
                                    />
                                    {errors.current_password && <small className="p-error block mt-1">{errors.current_password}</small>}
                                </div>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div className="field bg-white p-4 rounded-lg">
                                        <label className="text-sm font-medium text-gray-700 block mb-2">New Password</label>
                                        <Password
                                            value={newPassword}
                                            onChange={(e) => setNewPassword(e.target.value)}
                                            toggleMask
                                            className={`w-full ${errors.new_password ? 'p-invalid' : ''}`}
                                            inputClassName="w-full p-inputtext-sm p-3 rounded-lg border border-gray-300"
                                        />
                                        {errors.new_password && <small className="p-error block mt-1">{errors.new_password}</small>}
                                    </div>
                                        <div className="field bg-white p-4 rounded-lg">
                                        <label className="text-sm font-medium text-gray-700 block mb-2">Confirm Password</label>
                                        <Password
                                            value={confirmPassword}
                                            onChange={(e) => setConfirmPassword(e.target.value)}
                                            toggleMask
                                            className={`w-full ${errors.confirmPassword || (newPassword !== confirmPassword) ? 'p-invalid' : ''}`}
                                            inputClassName="w-full p-inputtext-sm p-3 rounded-lg border border-gray-300"
                                        />
                                        {errors.confirmPassword && <small className="p-error block mt-1">{errors.confirmPassword}</small>}
                                    </div>
                                </div>
                            </div>
                            <div className="flex gap-3 justify-end mt-6">
                                <Button label="Cancel" severity="secondary" className="p-button-sm"
                                    onClick={() => setIsEditingPassword(false)} />
                                <Button label="Update Password" className="p-button-sm main-btn"
                                    onClick={handleChangePassword}
                                    loading={loading} />
                            </div>
                        </>
                    )}
                </div>
            </div>
            </div>
        </section>
    );
}

export default SettingsIndex;
